
import { api } from './api';
import { Patient, PatientUpdateRequest, AppointmentRequest, AppointmentResponse, DoctorProfile } from '@/types/api';

export const patientService = {
  // Patient Profile
  async getProfile(patientId: number): Promise<Patient> {
    console.log('🚀 Fetching patient profile for ID:', patientId);

    try {
      const response = await api.get('/patients/profile', { patientId });
      console.log('✅ Patient profile fetched from backend:', response);

      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch patient profile:', error);
      throw error;
    }
  },

  async updateProfile(patientId: number, profileData: PatientUpdateRequest): Promise<Patient> {
    console.log('🚀 Updating patient profile for ID:', patientId, 'with data:', profileData);

    try {
      const response = await api.put('/patients/profile', profileData, { patientId });
      console.log('✅ Patient profile updated successfully:', response);

      return response;
    } catch (error: any) {
      console.error('❌ Failed to update patient profile:', error);
      throw error;
    }
  },

  // Appointments
  async createAppointment(appointmentData: AppointmentRequest): Promise<AppointmentResponse> {
    console.log('🚀 Creating appointment with complete data:', {
      patientId: appointmentData.patientId,
      doctorId: appointmentData.doctorId,
      specialty: appointmentData.specialty,
      appointmentDate: appointmentData.appointmentDate,
      reason: appointmentData.reason,
      notes: appointmentData.notes,
      clinicId: appointmentData.clinicId
    });

    const response = await api.post('/patients/appointments', appointmentData);
    console.log('✅ Appointment created successfully in database:', response);

    // Handle backend response format
    const appointment = response.success ? response.data : response;
    return appointment;
  },

  async getAppointments(patientId: number, status?: string): Promise<AppointmentResponse[]> {
    console.log('🚀 Fetching appointments for patient:', patientId, 'status:', status);

    try {
      // Use the documented patient appointments endpoint
      const params: any = {};
      if (status) params.status = status;

      const response = await api.get(`/patients/appointments`, { patientId, ...params });
      console.log('✅ Patient appointments fetched from backend:', response);

      // Handle backend response format - should be direct array
      return Array.isArray(response) ? response : [];

    } catch (error: any) {
      console.error('❌ Failed to fetch patient appointments:', error);

      // Fallback to alternative endpoint
      try {
        const params: any = {};
        if (status) params.status = status;

        const response = await api.get(`/appointments/patient/${patientId}`, params);
        console.log('✅ Patient appointments fetched from fallback endpoint:', response);

        return Array.isArray(response) ? response : [];
      } catch (fallbackError: any) {
        console.error('❌ Both appointment endpoints failed:', fallbackError);
        return [];
      }
    }
  },

  async getAppointmentById(appointmentId: number): Promise<AppointmentResponse> {
    console.log('🚀 Fetching appointment by ID:', appointmentId);

    const response = await api.get(`/appointments/${appointmentId}`);
    console.log('✅ Appointment fetched by ID:', response);

    return response;
  },

  async cancelAppointment(appointmentId: number, reason?: string): Promise<{ success: boolean; message: string }> {
    console.log('🚀 Cancelling appointment:', appointmentId, 'reason:', reason);

    try {
      const response = await api.put(`/patients/appointments/${appointmentId}/cancel`, { reason });
      console.log('✅ Appointment cancelled successfully:', response);

      return {
        success: true,
        message: response.message || 'Appointment cancelled successfully'
      };
    } catch (error: any) {
      console.error('❌ Failed to cancel appointment:', error);
      throw error;
    }
  },

  async getAppointmentHistory(patientId: number): Promise<AppointmentResponse[]> {
    console.log('🚀 Fetching appointment history for patient:', patientId);

    try {
      const response = await api.get('/patients/appointments/history', { patientId });
      console.log('✅ Appointment history fetched:', response);

      return Array.isArray(response) ? response : [];
    } catch (error: any) {
      console.error('❌ Failed to fetch appointment history:', error);
      return [];
    }
  },



  async rescheduleAppointment(appointmentId: number, newDate: string): Promise<void> {
    return await api.put(`/appointments/${appointmentId}/reschedule`, { appointmentDate: newDate });
  },

  // Doctor Search and Availability
  async searchDoctors(specialty?: string, location?: string, name?: string): Promise<DoctorProfile[]> {
    console.log('🚀 Searching doctors with specialty:', specialty);

    try {
      const params: any = {};
      if (specialty && specialty !== 'all') params.specialty = specialty;
      if (location) params.location = location;
      if (name) params.name = name;

      const response = await api.get('/patients/search/doctors', params);
      console.log('✅ Doctors fetched from backend:', response);

      // Handle backend response format
      const doctors = response.success ? response.data : (Array.isArray(response) ? response : []);
      return doctors;

    } catch (error: any) {
      console.error('❌ Doctor search API failed:', error);
      console.warn('Using fallback doctor data');

      // Return comprehensive mock data as fallback
      const allDoctors = [
      {
        id: 1,
        name: "Dr. Sarah Smith",
        email: "<EMAIL>",
        phoneNumber: "+**********",
        address: "123 Medical Center Dr",
        dateOfBirth: "1980-01-01",
        gender: "FEMALE",
        medicalLicense: "MD123456",
        specialtyName: "Cardiology",
        yearsOfExperience: 10,
        qualification: "MD, Cardiology",
        doctorStatus: "ACTIVE",
        clinicId: 1,
        clinicName: "Downtown Medical Center",
        consultationFee: 150,
        bio: "Experienced cardiologist specializing in heart disease prevention and treatment.",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z"
      },
      {
        id: 2,
        name: "Dr. Michael Johnson",
        email: "<EMAIL>",
        phoneNumber: "+**********",
        address: "456 Health Plaza",
        dateOfBirth: "1975-05-15",
        gender: "MALE",
        medicalLicense: "MD789012",
        specialtyName: "General Medicine",
        yearsOfExperience: 15,
        qualification: "MD, Family Medicine",
        doctorStatus: "ACTIVE",
        clinicId: 2,
        clinicName: "City General Hospital",
        consultationFee: 100,
        bio: "Family medicine physician providing comprehensive healthcare for all ages.",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z"
      },
      {
        id: 3,
        name: "Dr. Emily Davis",
        email: "<EMAIL>",
        phoneNumber: "+**********",
        address: "789 Wellness Ave",
        dateOfBirth: "1985-03-20",
        gender: "FEMALE",
        medicalLicense: "MD345678",
        specialtyName: "Dermatology",
        yearsOfExperience: 8,
        qualification: "MD, Dermatology",
        doctorStatus: "ACTIVE",
        clinicId: 1,
        clinicName: "Downtown Medical Center",
        consultationFee: 120,
        bio: "Dermatologist specializing in skin conditions and cosmetic procedures.",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z"
      },
      {
        id: 4,
        name: "Dr. Robert Wilson",
        email: "<EMAIL>",
        phoneNumber: "+**********",
        address: "321 Care Street",
        dateOfBirth: "1978-11-10",
        gender: "MALE",
        medicalLicense: "MD901234",
        specialtyName: "Orthopedics",
        yearsOfExperience: 12,
        qualification: "MD, Orthopedic Surgery",
        doctorStatus: "ACTIVE",
        clinicId: 3,
        clinicName: "Specialty Care Center",
        consultationFee: 180,
        bio: "Orthopedic surgeon specializing in joint replacement and sports medicine.",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z"
      }
    ];

    // Filter doctors based on specialty
    return allDoctors.filter(doctor => {
      if (specialty && specialty !== 'all') {
        // Map frontend specialty values to doctor specialty names
        const specialtyMap: { [key: string]: string } = {
          'CARDIOLOGY': 'Cardiology',
          'GENERAL_MEDICINE': 'General Medicine',
          'DERMATOLOGY': 'Dermatology',
          'ORTHOPEDICS': 'Orthopedics',
          'NEUROLOGY': 'Neurology',
          'PEDIATRICS': 'Pediatrics',
          'PSYCHIATRY': 'Psychiatry',
          'SURGERY': 'Surgery',
          'FAMILY_MEDICINE': 'General Medicine',
          'INTERNAL_MEDICINE': 'General Medicine'
        };

        const expectedSpecialtyName = specialtyMap[specialty] || specialty;
        if (!doctor.specialtyName.includes(expectedSpecialtyName)) return false;
      }
      if (name && !doctor.name.toLowerCase().includes(name.toLowerCase())) return false;
      return true;
    });
    }
  },

  async getDoctorAvailability(doctorId: number, date: string): Promise<string[]> {
    console.log('🚀 Fetching availability for doctor:', doctorId, 'date:', date);

    try {
      const response = await api.get(`/patients/doctors/${doctorId}/availability`, { date });
      console.log('✅ Doctor availability fetched from backend:', response);

      // Handle backend response format
      const slots = response.success ? response.data : (Array.isArray(response) ? response : []);
      return slots;

    } catch (error: any) {
      console.error('❌ Doctor availability API failed:', error);
      console.warn('Using fallback time slots');

      // Return fallback time slots for testing
      return [
        "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
        "14:00", "14:30", "15:00", "15:30", "16:00", "16:30"
      ];
    }
  },

  async getDoctorProfile(doctorId: number): Promise<DoctorProfile> {
    return await api.get(`/patients/doctors/${doctorId}/profile`);
  },

  // Medical History
  async getMedicalHistory(patientId: number): Promise<any> {
    return await api.get('/patients/medical-history', { patientId });
  },

  async getDiagnoses(patientId: number): Promise<any[]> {
    return await api.get('/patients/diagnoses', { patientId });
  },

  async getPrescriptions(patientId: number): Promise<any[]> {
    return await api.get('/patients/prescriptions', { patientId });
  },

  async getTestResults(patientId: number): Promise<any[]> {
    return await api.get('/patients/test-results', { patientId });
  },

  // Health Records
  async addHealthMetric(patientId: number, metricData: any): Promise<void> {
    return await api.post('/patients/health-metrics', metricData, { patientId });
  },

  async getHealthMetrics(patientId: number, type?: string): Promise<any[]> {
    const params: any = { patientId };
    if (type) params.type = type;
    return await api.get('/patients/health-metrics', params);
  },

  // Dashboard Stats
  async getDashboardStats(patientId: number): Promise<any> {
    console.log('🚀 Fetching dashboard stats for patient:', patientId);

    try {
      const response = await api.get('/patients/dashboard/stats', { patientId });
      console.log('✅ Dashboard stats fetched from backend:', response);

      // Handle backend response format
      const stats = response.success ? response.data : response;
      return stats;

    } catch (error: any) {
      console.error('❌ Dashboard stats API failed:', error);
      console.warn('Using fallback dashboard stats');

      // Return mock stats as fallback
      return {
        totalAppointments: 12,
        upcomingAppointments: 2,
        completedAppointments: 10,
        activePrescriptions: 3,
        healthScore: "95%",
        lastCheckup: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      };
    }
  }
};
