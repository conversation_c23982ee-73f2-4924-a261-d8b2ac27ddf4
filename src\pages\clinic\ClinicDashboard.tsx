
import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  Users,
  Calendar,
  Activity,
  DollarSign,
  TrendingUp,
  Clock,
  UserCheck,
  AlertCircle,
  Plus,
  Eye,
  LogOut
} from "lucide-react";
import { clinicService } from "@/services/clinicService";
import { ClinicDashboardStats } from "@/types/api";
import { useNotification } from "@/context/NotificationContext";
import { useNavigate } from "react-router-dom";

const ClinicDashboard = () => {
  const { user, logout } = useAuth();
  const { showToast } = useNotification();
  const navigate = useNavigate();
  const [stats, setStats] = useState<ClinicDashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [recentAppointments, setRecentAppointments] = useState([]);
  const [todaySchedule, setTodaySchedule] = useState([]);

  const clinicId = user?.id || 1; // Get clinic ID from user context

  useEffect(() => {
    fetchDashboardData();
  }, [clinicId]);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // Fetch dashboard stats
      const dashboardStats = await clinicService.getDashboardStats(clinicId);
      setStats(dashboardStats);

      // Fetch today's appointments
      const todayAppts = await clinicService.getTodayAppointments(clinicId);
      setTodaySchedule(todayAppts);

      // Fetch recent appointments
      const recentAppts = await clinicService.getUpcomingAppointments(clinicId);
      setRecentAppointments(recentAppts);

    } catch (error: any) {
      console.error('Failed to fetch dashboard data:', error);
      showToast('Error', 'Failed to load dashboard data', 'error');

      // Fallback to mock data for development
      setStats({
        totalStaff: 24,
        totalDoctors: 8,
        todayAppointments: 45,
        totalPatients: 1250,
        monthlyRevenue: 125000,
        appointmentStats: {
          scheduled: 32,
          completed: 28,
          cancelled: 3,
          pending: 12
        },
        departmentStats: [
          { name: "Cardiology", staffCount: 6, appointmentsToday: 12 },
          { name: "Pediatrics", staffCount: 4, appointmentsToday: 8 },
          { name: "General Medicine", staffCount: 8, appointmentsToday: 15 },
          { name: "Emergency", staffCount: 6, appointmentsToday: 10 }
        ]
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      showToast('Success', 'Logged out successfully', 'success');
    } catch (error: any) {
      showToast('Error', 'Failed to logout', 'error');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  const dashboardCards = [
    {
      title: "Total Staff",
      value: stats?.totalStaff?.toString() || "0",
      icon: Users,
      color: "text-blue-600",
      change: "+2 this month"
    },
    {
      title: "Today's Appointments",
      value: stats?.todayAppointments?.toString() || "0",
      icon: Calendar,
      color: "text-green-600",
      change: "+12% from yesterday"
    },
    {
      title: "Active Doctors",
      value: stats?.totalDoctors?.toString() || "0",
      icon: Activity,
      color: "text-orange-600",
      change: "All available"
    },
    {
      title: "Monthly Revenue",
      value: `$${stats?.monthlyRevenue?.toLocaleString() || "0"}`,
      icon: DollarSign,
      color: "text-purple-600",
      change: "+8.2% from last month"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Clinic Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user?.name}</p>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" onClick={() => navigate('/clinic/staff')}>
                <Users className="h-4 w-4 mr-2" />
                Manage Staff
              </Button>
              <Button variant="outline" onClick={() => navigate('/clinic/appointments')}>
                <Calendar className="h-4 w-4 mr-2" />
                View Appointments
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {dashboardCards.map((card, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{card.value}</div>
                <p className="text-xs text-gray-600 mt-1">{card.change}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="appointments">Appointments</TabsTrigger>
            <TabsTrigger value="departments">Departments</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Today's Schedule */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Today's Schedule</CardTitle>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View All
                    </Button>
                  </div>
                  <CardDescription>Upcoming appointments for today</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { time: "09:00 AM", patient: "John Smith", doctor: "Dr. Sarah Johnson", type: "Consultation" },
                      { time: "10:30 AM", patient: "Mary Davis", doctor: "Dr. Michael Chen", type: "Follow-up" },
                      { time: "02:00 PM", patient: "Robert Wilson", doctor: "Dr. Emily Rodriguez", type: "Check-up" },
                      { time: "03:30 PM", patient: "Lisa Brown", doctor: "Dr. James Wilson", type: "Surgery Consultation" }
                    ].map((appointment, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                            <Clock className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">{appointment.patient}</p>
                            <p className="text-sm text-gray-600">{appointment.doctor}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{appointment.time}</p>
                          <p className="text-sm text-gray-600">{appointment.type}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Staff Status */}
              <Card>
                <CardHeader>
                  <CardTitle>Staff Status</CardTitle>
                  <CardDescription>Current staff availability</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: "Dr. Sarah Johnson", role: "Cardiologist", status: "Available", patients: 8 },
                      { name: "Dr. Michael Chen", role: "Dermatologist", status: "In Surgery", patients: 5 },
                      { name: "Dr. Emily Rodriguez", role: "Pediatrician", status: "Available", patients: 12 },
                      { name: "Dr. James Wilson", role: "Orthopedic", status: "On Break", patients: 3 }
                    ].map((staff, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
                            <UserCheck className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium">{staff.name}</p>
                            <p className="text-sm text-gray-600">{staff.role}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className={
                            staff.status === 'Available' ? 'bg-green-100 text-green-800' :
                            staff.status === 'In Surgery' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }>
                            {staff.status}
                          </Badge>
                          <p className="text-sm text-gray-600 mt-1">{staff.patients} patients</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="appointments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Appointment Statistics</CardTitle>
                <CardDescription>Today's appointment breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{stats?.appointmentStats.scheduled || 0}</div>
                    <div className="text-sm text-gray-600">Scheduled</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{stats?.appointmentStats.completed || 0}</div>
                    <div className="text-sm text-gray-600">Completed</div>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">{stats?.appointmentStats.pending || 0}</div>
                    <div className="text-sm text-gray-600">Pending</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{stats?.appointmentStats.cancelled || 0}</div>
                    <div className="text-sm text-gray-600">Cancelled</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="departments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Department Overview</CardTitle>
                <CardDescription>Staff and appointments by department</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats?.departmentStats.map((dept, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg">
                          <Building2 className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium">{dept.name}</h3>
                          <p className="text-sm text-gray-600">{dept.staffCount} staff members</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{dept.appointmentsToday}</div>
                        <div className="text-sm text-gray-600">appointments today</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Trends</CardTitle>
                  <CardDescription>Monthly revenue performance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600">Revenue chart will be displayed here</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Patient Flow</CardTitle>
                  <CardDescription>Daily patient statistics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
                    <div className="text-center">
                      <Activity className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600">Patient flow chart will be displayed here</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ClinicDashboard;
