// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  email: string;
  role: 'ADMIN' | 'DOCTOR' | 'PATIENT' | 'CLINIC';
  userId: number;
  name: string;
  message: string;
}

export interface PatientRegisterRequest {
  name: string;
  email: string;
  password: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string; // YYYY-MM-DD
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  role: 'PATIENT';
  emergencyContactName: string;
  emergencyContactPhone: string;
  bloodGroup: string;
  allergies?: string;
  medicalHistory?: string;
}

export interface DoctorRegisterRequest {
  name: string;
  email: string;
  password: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  role: 'DOCTOR';
  medicalLicense: string;
  yearsOfExperience: number;
  qualification: string;
  consultationFee: number;
  bio: string;
}

export interface ClinicRegisterRequest {
  name: string;
  email: string;
  password: string;
  phoneNumber: string;
  address: string;
  dateOfBirth?: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER' | 'ORGANIZATION';
  role: 'CLINIC';
  clinicName: string;
  licenseNumber: string;
  description: string;
  operatingHours: string;
  emergencyContact: string;
  websiteUrl?: string;
}

// User Types
export interface User {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  role: string;
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING_APPROVAL' | 'SUSPENDED';
  createdAt: string;
  updatedAt: string;
  lastLogin: string;
}

// Doctor Types
export interface DoctorProfile {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  medicalLicense: string;
  specialtyName: string;
  yearsOfExperience: number;
  qualification: string;
  doctorStatus: string;
  clinicId: number;
  clinicName: string;
  consultationFee: number;
  bio: string;
  createdAt: string;
  updatedAt: string;
}

// Patient Types
export interface Patient {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  patientId: string;
  bloodGroup: string;
  allergies: string;
  medicalHistory: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
}

export interface PatientUpdateRequest {
  name: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  bloodGroup: string;
  allergies: string;
  medicalHistory: string;
  insuranceProvider: string;
  insuranceNumber: string;
}

// Clinic Types
export interface ClinicProfile {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  clinicName: string;
  description: string;
  clinicStatus: string;
  operatingHours: string;
  emergencyContact: string;
  websiteUrl: string;
  totalDoctors: number;
  totalStaff: number;
  licenseNumber?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ClinicStaff {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  role: 'DOCTOR' | 'NURSE' | 'TECHNICIAN' | 'RECEPTIONIST' | 'ADMIN';
  department: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ON_LEAVE';
  hireDate: string;
  specialization?: string;
  licenseNumber?: string;
}

export interface ClinicDashboardStats {
  totalStaff: number;
  totalDoctors: number;
  todayAppointments: number;
  totalPatients: number;
  monthlyRevenue: number;
  appointmentStats: {
    scheduled: number;
    completed: number;
    cancelled: number;
    pending: number;
  };
  departmentStats: {
    name: string;
    staffCount: number;
    appointmentsToday: number;
  }[];
}

// Appointment Types
export interface AppointmentRequest {
  patientId: number;
  doctorId: number;
  appointmentDate: string; // ISO datetime
  reason: string;
  notes?: string;
  durationMinutes?: number;
}

export interface AppointmentResponse {
  id: number;
  patientId: number;
  patientName: string;
  doctorId: number;
  doctorName: string;
  doctorSpecialty: string;
  clinicId: number;
  clinicName: string;
  appointmentDate: string;
  status: 'SCHEDULED' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
  reason: string;
  notes: string;
  durationMinutes: number;
  createdAt: string;
}

// Diagnosis Types
export interface DiagnosisRequest {
  doctorId: number;
  appointmentId: number;
  diagnosisCode: string;
  diagnosisDescription: string;
  symptoms: string;
  treatmentPlan: string;
  doctorNotes: string;
}

// Follow-up Types
export interface FollowUpRequest {
  patientId: number;
  doctorId: number;
  scheduledDate: string; // ISO datetime
  reason: string;
  notes: string;
}

// Announcement Types
export interface AnnouncementRequest {
  title: string;
  content: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  startDate: string; // ISO datetime
  endDate: string; // ISO datetime
}

// Admin Reports
export interface SystemReport {
  totalUsers: number;
  totalDoctors: number;
  totalClinics: number;
  totalPatients: number;
  totalAppointments: number;
  pendingDoctorApprovals: number;
  pendingClinicApprovals: number;
  todayAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
}

// API Response wrapper
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Error Response
export interface ApiError {
  message: string;
  status: number;
  timestamp: string;
}
