import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  Users,
  Stethoscope,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Globe,
  Clock,
  Shield,
  Edit,
  Save,
  X,
  LogOut
} from "lucide-react";
import { clinicService } from "@/services/clinicService";
import { ClinicProfile as ClinicProfileType } from "@/types/api";
import { useNotification } from "@/context/NotificationContext";

const ClinicProfile = () => {
  const { user, logout } = useAuth();
  const { showToast } = useNotification();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [profile, setProfile] = useState<ClinicProfileType | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    clinicName: "",
    email: "",
    phoneNumber: "",
    address: "",
    description: "",
    operatingHours: "",
    emergencyContact: "",
    websiteUrl: "",
    licenseNumber: ""
  });

  const clinicId = user?.id || 1;

  useEffect(() => {
    fetchClinicProfile();
  }, [clinicId]);

  const fetchClinicProfile = async () => {
    try {
      setIsLoading(true);
      const profileData = await clinicService.getProfile(clinicId);
      setProfile(profileData);
      setFormData({
        name: profileData.name || "",
        clinicName: profileData.clinicName || "",
        email: profileData.email || "",
        phoneNumber: profileData.phoneNumber || "",
        address: profileData.address || "",
        description: profileData.description || "",
        operatingHours: profileData.operatingHours || "",
        emergencyContact: profileData.emergencyContact || "",
        websiteUrl: profileData.websiteUrl || "",
        licenseNumber: profileData.licenseNumber || ""
      });
    } catch (error: any) {
      console.error('Failed to fetch clinic profile:', error);
      showToast('Error', 'Failed to load clinic profile', 'error');
      
      // Fallback to mock data
      const mockProfile = {
        id: clinicId,
        name: "Central Medical Center",
        clinicName: "Central Medical Center",
        email: "<EMAIL>",
        phoneNumber: "+1 ************",
        address: "123 Medical Center Drive, Healthcare City, HC 12345",
        description: "A comprehensive healthcare facility providing quality medical services to our community for over 20 years.",
        clinicStatus: "ACTIVE",
        operatingHours: "Mon-Fri: 8AM-8PM, Sat-Sun: 9AM-5PM",
        emergencyContact: "****** 567 8911",
        websiteUrl: "https://www.centralmedical.com",
        totalDoctors: 8,
        totalStaff: 24,
        licenseNumber: "CLN123456"
      };
      setProfile(mockProfile);
      setFormData({
        name: mockProfile.name,
        clinicName: mockProfile.clinicName,
        email: mockProfile.email,
        phoneNumber: mockProfile.phoneNumber,
        address: mockProfile.address,
        description: mockProfile.description,
        operatingHours: mockProfile.operatingHours,
        emergencyContact: mockProfile.emergencyContact,
        websiteUrl: mockProfile.websiteUrl,
        licenseNumber: mockProfile.licenseNumber
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      await clinicService.updateProfile(clinicId, formData);
      showToast('Success', 'Clinic profile updated successfully', 'success');
      setIsEditing(false);
      fetchClinicProfile();
    } catch (error: any) {
      showToast('Error', 'Failed to update clinic profile', 'error');
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        name: profile.name || "",
        clinicName: profile.clinicName || "",
        email: profile.email || "",
        phoneNumber: profile.phoneNumber || "",
        address: profile.address || "",
        description: profile.description || "",
        operatingHours: profile.operatingHours || "",
        emergencyContact: profile.emergencyContact || "",
        websiteUrl: profile.websiteUrl || "",
        licenseNumber: profile.licenseNumber || ""
      });
    }
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogout = async () => {
    try {
      await logout();
      showToast('Success', 'Logged out successfully', 'success');
    } catch (error: any) {
      showToast('Error', 'Failed to logout', 'error');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading clinic profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Clinic Profile</h1>
              <p className="text-gray-600">Manage your clinic information and settings</p>
            </div>
            <div className="flex items-center space-x-3">
              <Badge className={
                profile?.clinicStatus === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                profile?.clinicStatus === 'PENDING_APPROVAL' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }>
                {profile?.clinicStatus || 'ACTIVE'}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile">Profile Information</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="lg:col-span-2">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                      <Building2 className="h-5 w-5" />
                      <span>Clinic Information</span>
                    </CardTitle>
                    <div className="flex space-x-2">
                      {isEditing ? (
                        <>
                          <Button size="sm" onClick={handleSave}>
                            <Save className="h-4 w-4 mr-2" />
                            Save
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleCancel}>
                            <X className="h-4 w-4 mr-2" />
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="clinicName" className="flex items-center">
                        <Building2 className="h-3 w-3 mr-1" />
                        Clinic Name
                      </Label>
                      <Input 
                        id="clinicName"
                        value={formData.clinicName}
                        onChange={(e) => handleInputChange('clinicName', e.target.value)}
                        disabled={!isEditing} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="licenseNumber" className="flex items-center">
                        <Shield className="h-3 w-3 mr-1" />
                        License Number
                      </Label>
                      <Input 
                        id="licenseNumber"
                        value={formData.licenseNumber}
                        onChange={(e) => handleInputChange('licenseNumber', e.target.value)}
                        disabled={!isEditing} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        Email Address
                      </Label>
                      <Input 
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        disabled={!isEditing} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phoneNumber" className="flex items-center">
                        <Phone className="h-3 w-3 mr-1" />
                        Phone Number
                      </Label>
                      <Input 
                        id="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                        disabled={!isEditing} 
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address" className="flex items-center">
                      <MapPin className="h-3 w-3 mr-1" />
                      Address
                    </Label>
                    <Textarea 
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      disabled={!isEditing}
                      rows={3}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea 
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      disabled={!isEditing}
                      rows={4}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="operatingHours" className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        Operating Hours
                      </Label>
                      <Input 
                        id="operatingHours"
                        value={formData.operatingHours}
                        onChange={(e) => handleInputChange('operatingHours', e.target.value)}
                        disabled={!isEditing} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="emergencyContact" className="flex items-center">
                        <Phone className="h-3 w-3 mr-1" />
                        Emergency Contact
                      </Label>
                      <Input 
                        id="emergencyContact"
                        value={formData.emergencyContact}
                        onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                        disabled={!isEditing} 
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="websiteUrl" className="flex items-center">
                      <Globe className="h-3 w-3 mr-1" />
                      Website URL
                    </Label>
                    <Input 
                      id="websiteUrl"
                      value={formData.websiteUrl}
                      onChange={(e) => handleInputChange('websiteUrl', e.target.value)}
                      disabled={!isEditing} 
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="h-5 w-5" />
                    <span>Quick Stats</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{profile?.totalStaff || 24}</div>
                    <div className="text-sm text-gray-600">Total Staff</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{profile?.totalDoctors || 8}</div>
                    <div className="text-sm text-gray-600">Active Doctors</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">6</div>
                    <div className="text-sm text-gray-600">Departments</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">1,250</div>
                    <div className="text-sm text-gray-600">Patients Served</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ClinicProfile;
