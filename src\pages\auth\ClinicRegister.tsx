import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Building2, 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Clock, 
  Shield, 
  Eye, 
  EyeOff,
  ArrowLeft,
  CheckCircle
} from "lucide-react";
import { clinicRegisterSchema, ClinicRegisterFormData } from "@/schemas/validation";
import { authService } from "@/services/authService";
import { useNotification } from "@/context/NotificationContext";

const ClinicRegister = () => {
  const navigate = useNavigate();
  const { showToast } = useNotification();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    trigger
  } = useForm<ClinicRegisterFormData>({
    resolver: zodResolver(clinicRegisterSchema),
    defaultValues: {
      role: "CLINIC",
      gender: "ORGANIZATION"
    }
  });

  const watchedFields = watch();

  const onSubmit = async (data: ClinicRegisterFormData) => {
    if (!acceptedTerms) {
      showToast('Error', 'Please accept the terms and conditions', 'error');
      return;
    }

    try {
      setIsLoading(true);
      
      const registrationData = {
        ...data,
        role: "CLINIC" as const,
        gender: "ORGANIZATION" as const
      };

      await authService.register(registrationData);
      
      showToast(
        'Success', 
        'Clinic registration successful! Please check your email for verification instructions.', 
        'success'
      );
      
      navigate('/login', { 
        state: { 
          message: 'Registration successful! Please log in with your credentials.',
          email: data.email 
        } 
      });
    } catch (error: any) {
      console.error('Registration failed:', error);
      const errorMessage = error.response?.data?.message || 'Registration failed. Please try again.';
      showToast('Error', errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const nextStep = async () => {
    const fieldsToValidate = currentStep === 1 
      ? ['name', 'email', 'password', 'phoneNumber']
      : ['clinicName', 'licenseNumber', 'address', 'description'];
    
    const isValid = await trigger(fieldsToValidate as any);
    if (isValid) {
      setCurrentStep(2);
    }
  };

  const prevStep = () => {
    setCurrentStep(1);
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Basic Information</h2>
        <p className="text-gray-600">Let's start with your basic clinic details</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name" className="flex items-center">
            <Building2 className="h-4 w-4 mr-2" />
            Administrator Name *
          </Label>
          <Input
            id="name"
            {...register("name")}
            placeholder="John Smith"
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && (
            <p className="text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="flex items-center">
            <Mail className="h-4 w-4 mr-2" />
            Email Address *
          </Label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="<EMAIL>"
            className={errors.email ? "border-red-500" : ""}
          />
          {errors.email && (
            <p className="text-sm text-red-500">{errors.email.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="password" className="flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            Password *
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              {...register("password")}
              placeholder="Enter secure password"
              className={errors.password ? "border-red-500" : ""}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          {errors.password && (
            <p className="text-sm text-red-500">{errors.password.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="phoneNumber" className="flex items-center">
            <Phone className="h-4 w-4 mr-2" />
            Phone Number *
          </Label>
          <Input
            id="phoneNumber"
            {...register("phoneNumber")}
            placeholder="+****************"
            className={errors.phoneNumber ? "border-red-500" : ""}
          />
          {errors.phoneNumber && (
            <p className="text-sm text-red-500">{errors.phoneNumber.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="address" className="flex items-center">
          <MapPin className="h-4 w-4 mr-2" />
          Clinic Address *
        </Label>
        <Textarea
          id="address"
          {...register("address")}
          placeholder="123 Medical Center Drive, Healthcare City, HC 12345"
          rows={3}
          className={errors.address ? "border-red-500" : ""}
        />
        {errors.address && (
          <p className="text-sm text-red-500">{errors.address.message}</p>
        )}
      </div>

      <div className="flex justify-end">
        <Button onClick={nextStep} className="px-8">
          Next Step
        </Button>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Clinic Details</h2>
        <p className="text-gray-600">Complete your clinic information</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="clinicName" className="flex items-center">
            <Building2 className="h-4 w-4 mr-2" />
            Clinic Name *
          </Label>
          <Input
            id="clinicName"
            {...register("clinicName")}
            placeholder="Central Medical Center"
            className={errors.clinicName ? "border-red-500" : ""}
          />
          {errors.clinicName && (
            <p className="text-sm text-red-500">{errors.clinicName.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="licenseNumber" className="flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            License Number *
          </Label>
          <Input
            id="licenseNumber"
            {...register("licenseNumber")}
            placeholder="CLN123456"
            className={errors.licenseNumber ? "border-red-500" : ""}
          />
          {errors.licenseNumber && (
            <p className="text-sm text-red-500">{errors.licenseNumber.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description" className="flex items-center">
          <Building2 className="h-4 w-4 mr-2" />
          Clinic Description *
        </Label>
        <Textarea
          id="description"
          {...register("description")}
          placeholder="Describe your clinic's services and specialties..."
          rows={4}
          className={errors.description ? "border-red-500" : ""}
        />
        {errors.description && (
          <p className="text-sm text-red-500">{errors.description.message}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="operatingHours" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            Operating Hours *
          </Label>
          <Input
            id="operatingHours"
            {...register("operatingHours")}
            placeholder="Mon-Fri: 8AM-6PM, Sat: 9AM-2PM"
            className={errors.operatingHours ? "border-red-500" : ""}
          />
          {errors.operatingHours && (
            <p className="text-sm text-red-500">{errors.operatingHours.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="emergencyContact" className="flex items-center">
            <Phone className="h-4 w-4 mr-2" />
            Emergency Contact *
          </Label>
          <Input
            id="emergencyContact"
            {...register("emergencyContact")}
            placeholder="+****************"
            className={errors.emergencyContact ? "border-red-500" : ""}
          />
          {errors.emergencyContact && (
            <p className="text-sm text-red-500">{errors.emergencyContact.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="websiteUrl" className="flex items-center">
          <Globe className="h-4 w-4 mr-2" />
          Website URL (Optional)
        </Label>
        <Input
          id="websiteUrl"
          {...register("websiteUrl")}
          placeholder="https://www.yourClinic.com"
          className={errors.websiteUrl ? "border-red-500" : ""}
        />
        {errors.websiteUrl && (
          <p className="text-sm text-red-500">{errors.websiteUrl.message}</p>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="terms"
          checked={acceptedTerms}
          onCheckedChange={(checked) => setAcceptedTerms(checked as boolean)}
        />
        <Label htmlFor="terms" className="text-sm">
          I agree to the{" "}
          <Link to="/terms" className="text-blue-600 hover:underline">
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link to="/privacy" className="text-blue-600 hover:underline">
            Privacy Policy
          </Link>
        </Label>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={prevStep}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <Button 
          type="submit" 
          disabled={isLoading || !acceptedTerms}
          className="px-8"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Registering...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Register Clinic
            </>
          )}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <Building2 className="h-12 w-12 text-blue-600" />
          </div>
          <CardTitle className="text-3xl font-bold">Register Your Clinic</CardTitle>
          <CardDescription>
            Join MediConnect to manage your clinic operations efficiently
          </CardDescription>
          
          {/* Progress Indicator */}
          <div className="flex items-center justify-center mt-6">
            <div className="flex items-center space-x-4">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                1
              </div>
              <div className={`w-16 h-1 ${currentStep >= 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                2
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            {currentStep === 1 ? renderStep1() : renderStep2()}
          </form>
          
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link to="/login" className="text-blue-600 hover:underline font-medium">
                Sign in here
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClinicRegister;
