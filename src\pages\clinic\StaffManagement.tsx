
import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Mail,
  Phone,
  Filter,
  UserPlus,
  Users,
  Stethoscope,
  Activity,
  LogOut
} from "lucide-react";
import { clinicService } from "@/services/clinicService";
import { ClinicStaff } from "@/types/api";
import { useNotification } from "@/context/NotificationContext";

const StaffManagement = () => {
  const { user, logout } = useAuth();
  const { showToast } = useNotification();
  const [doctors, setDoctors] = useState<ClinicStaff[]>([]);
  const [staff, setStaff] = useState<ClinicStaff[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isAddStaffOpen, setIsAddStaffOpen] = useState(false);
  const [isAddDoctorOpen, setIsAddDoctorOpen] = useState(false);
  const [newStaffData, setNewStaffData] = useState({
    name: "",
    email: "",
    phoneNumber: "",
    role: "",
    department: "",
    specialization: "",
    licenseNumber: ""
  });

  const clinicId = user?.id || 1;

  useEffect(() => {
    fetchStaffData();
  }, [clinicId]);

  const fetchStaffData = async () => {
    try {
      setIsLoading(true);

      // Fetch doctors
      const doctorsData = await clinicService.getDoctors(clinicId);
      setDoctors(doctorsData);

      // Fetch other staff
      const staffData = await clinicService.getStaff(clinicId);
      setStaff(staffData.filter(member => member.role !== 'DOCTOR'));

    } catch (error: any) {
      console.error('Failed to fetch staff data:', error);
      showToast('Error', 'Failed to load staff data', 'error');

      // Fallback to mock data
      setDoctors([
        {
          id: 1,
          name: "Dr. Sarah Johnson",
          email: "<EMAIL>",
          phoneNumber: "****** 567 8901",
          role: "DOCTOR",
          department: "Cardiology",
          status: "ACTIVE",
          hireDate: "2023-01-15",
          specialization: "Cardiology",
          licenseNumber: "MD12345"
        },
        {
          id: 2,
          name: "Dr. Michael Brown",
          email: "<EMAIL>",
          phoneNumber: "****** 567 8902",
          role: "DOCTOR",
          department: "Neurology",
          status: "ACTIVE",
          hireDate: "2023-02-20",
          specialization: "Neurology",
          licenseNumber: "MD12346"
        }
      ]);

      setStaff([
        {
          id: 3,
          name: "Alice Smith",
          email: "<EMAIL>",
          phoneNumber: "****** 567 8903",
          role: "NURSE",
          department: "Emergency",
          status: "ACTIVE",
          hireDate: "2023-03-10"
        },
        {
          id: 4,
          name: "Bob Wilson",
          email: "<EMAIL>",
          phoneNumber: "****** 567 8904",
          role: "TECHNICIAN",
          department: "Lab",
          status: "ACTIVE",
          hireDate: "2023-04-05"
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddStaff = async () => {
    try {
      await clinicService.addStaffMember(clinicId, newStaffData);
      showToast('Success', 'Staff member added successfully', 'success');
      setIsAddStaffOpen(false);
      setNewStaffData({
        name: "",
        email: "",
        phoneNumber: "",
        role: "",
        department: "",
        specialization: "",
        licenseNumber: ""
      });
      fetchStaffData();
    } catch (error: any) {
      showToast('Error', 'Failed to add staff member', 'error');
    }
  };

  const handleAddDoctor = async () => {
    try {
      await clinicService.addDoctor(clinicId, newStaffData);
      showToast('Success', 'Doctor added successfully', 'success');
      setIsAddDoctorOpen(false);
      setNewStaffData({
        name: "",
        email: "",
        phoneNumber: "",
        role: "",
        department: "",
        specialization: "",
        licenseNumber: ""
      });
      fetchStaffData();
    } catch (error: any) {
      showToast('Error', 'Failed to add doctor', 'error');
    }
  };

  const handleRemoveStaff = async (staffId: number, isDoctor: boolean = false) => {
    try {
      if (isDoctor) {
        await clinicService.removeDoctor(clinicId, staffId);
      } else {
        await clinicService.removeStaffMember(staffId);
      }
      showToast('Success', 'Staff member removed successfully', 'success');
      fetchStaffData();
    } catch (error: any) {
      showToast('Error', 'Failed to remove staff member', 'error');
    }
  };

  const filteredDoctors = doctors.filter(doctor => {
    const matchesSearch = doctor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doctor.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || doctor.status.toLowerCase() === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredStaff = staff.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || member.status.toLowerCase() === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleLogout = async () => {
    try {
      await logout();
      showToast('Success', 'Logged out successfully', 'success');
    } catch (error: any) {
      showToast('Error', 'Failed to logout', 'error');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'on_leave': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading staff data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Staff Management</h1>
              <p className="text-gray-600">Manage doctors and clinic staff members</p>
            </div>
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm text-gray-600">Welcome back,</p>
                <p className="font-medium text-gray-900">{user?.name}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>

          <div className="flex space-x-3 mt-4">
            <Dialog open={isAddDoctorOpen} onOpenChange={setIsAddDoctorOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Stethoscope className="h-4 w-4 mr-2" />
                  Add Doctor
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Doctor</DialogTitle>
                  <DialogDescription>
                    Add a new doctor to your clinic staff.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">Name</Label>
                    <Input
                      id="name"
                      value={newStaffData.name}
                      onChange={(e) => setNewStaffData({...newStaffData, name: e.target.value})}
                      className="col-span-3"
                      placeholder="Dr. John Smith"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newStaffData.email}
                      onChange={(e) => setNewStaffData({...newStaffData, email: e.target.value})}
                      className="col-span-3"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phone" className="text-right">Phone</Label>
                    <Input
                      id="phone"
                      value={newStaffData.phoneNumber}
                      onChange={(e) => setNewStaffData({...newStaffData, phoneNumber: e.target.value})}
                      className="col-span-3"
                      placeholder="+1234567890"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="specialization" className="text-right">Specialty</Label>
                    <Input
                      id="specialization"
                      value={newStaffData.specialization}
                      onChange={(e) => setNewStaffData({...newStaffData, specialization: e.target.value})}
                      className="col-span-3"
                      placeholder="Cardiology"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="license" className="text-right">License</Label>
                    <Input
                      id="license"
                      value={newStaffData.licenseNumber}
                      onChange={(e) => setNewStaffData({...newStaffData, licenseNumber: e.target.value})}
                      className="col-span-3"
                      placeholder="MD12345"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" onClick={handleAddDoctor}>Add Doctor</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isAddStaffOpen} onOpenChange={setIsAddStaffOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Staff Member
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Staff Member</DialogTitle>
                  <DialogDescription>
                    Add a new staff member to your clinic.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="staff-name" className="text-right">Name</Label>
                    <Input
                      id="staff-name"
                      value={newStaffData.name}
                      onChange={(e) => setNewStaffData({...newStaffData, name: e.target.value})}
                      className="col-span-3"
                      placeholder="John Smith"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="staff-email" className="text-right">Email</Label>
                    <Input
                      id="staff-email"
                      type="email"
                      value={newStaffData.email}
                      onChange={(e) => setNewStaffData({...newStaffData, email: e.target.value})}
                      className="col-span-3"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="staff-phone" className="text-right">Phone</Label>
                    <Input
                      id="staff-phone"
                      value={newStaffData.phoneNumber}
                      onChange={(e) => setNewStaffData({...newStaffData, phoneNumber: e.target.value})}
                      className="col-span-3"
                      placeholder="+1234567890"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="staff-role" className="text-right">Role</Label>
                    <Select onValueChange={(value) => setNewStaffData({...newStaffData, role: value})}>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="NURSE">Nurse</SelectItem>
                        <SelectItem value="TECHNICIAN">Technician</SelectItem>
                        <SelectItem value="RECEPTIONIST">Receptionist</SelectItem>
                        <SelectItem value="ADMIN">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="staff-department" className="text-right">Department</Label>
                    <Input
                      id="staff-department"
                      value={newStaffData.department}
                      onChange={(e) => setNewStaffData({...newStaffData, department: e.target.value})}
                      className="col-span-3"
                      placeholder="Emergency"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" onClick={handleAddStaff}>Add Staff Member</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search staff members..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="on_leave">On Leave</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs defaultValue="doctors" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="doctors" className="flex items-center space-x-2">
              <Stethoscope className="h-4 w-4" />
              <span>Doctors ({filteredDoctors.length})</span>
            </TabsTrigger>
            <TabsTrigger value="staff" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Staff ({filteredStaff.length})</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="doctors" className="space-y-4">
            {filteredDoctors.length === 0 ? (
              <Card>
                <CardContent className="flex items-center justify-center h-48">
                  <div className="text-center">
                    <Stethoscope className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No doctors found</p>
                    <p className="text-sm text-gray-500">Try adjusting your search or filters</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              filteredDoctors.map((doctor) => (
                <Card key={doctor.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full">
                          <Stethoscope className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{doctor.name}</CardTitle>
                          <CardDescription>{doctor.specialization || doctor.department}</CardDescription>
                        </div>
                      </div>
                      <Badge className={getStatusColor(doctor.status)}>
                        {doctor.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <Label className="text-sm font-medium flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          Email
                        </Label>
                        <p className="text-sm text-gray-600">{doctor.email}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          Phone
                        </Label>
                        <p className="text-sm text-gray-600">{doctor.phoneNumber}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">License</Label>
                        <p className="text-sm text-gray-600">{doctor.licenseNumber || 'N/A'}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveStaff(doctor.id, true)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </TabsContent>

          <TabsContent value="staff" className="space-y-4">
            {filteredStaff.length === 0 ? (
              <Card>
                <CardContent className="flex items-center justify-center h-48">
                  <div className="text-center">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No staff members found</p>
                    <p className="text-sm text-gray-500">Try adjusting your search or filters</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              filteredStaff.map((member) => (
                <Card key={member.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full">
                          <Activity className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{member.name}</CardTitle>
                          <CardDescription>{member.role} - {member.department}</CardDescription>
                        </div>
                      </div>
                      <Badge className={getStatusColor(member.status)}>
                        {member.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <Label className="text-sm font-medium flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          Email
                        </Label>
                        <p className="text-sm text-gray-600">{member.email}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          Phone
                        </Label>
                        <p className="text-sm text-gray-600">{member.phoneNumber}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Hire Date</Label>
                        <p className="text-sm text-gray-600">{new Date(member.hireDate).toLocaleDateString()}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveStaff(member.id, false)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default StaffManagement;
