import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { patientService } from '@/services/patientService';
import { Patient, PatientUpdateRequest, AppointmentRequest, AppointmentResponse, DoctorProfile } from '@/types/api';

// Query Keys
export const patientKeys = {
  all: ['patient'] as const,
  profile: (id: number) => [...patientKeys.all, 'profile', id] as const,
  appointments: (id: number) => [...patientKeys.all, 'appointments', id] as const,
  appointmentHistory: (id: number) => [...patientKeys.all, 'appointments', 'history', id] as const,
  medicalHistory: (id: number) => [...patientKeys.all, 'medical-history', id] as const,
  diagnoses: (id: number) => [...patientKeys.all, 'diagnoses', id] as const,
  prescriptions: (id: number) => [...patientKeys.all, 'prescriptions', id] as const,
  doctors: (filters?: any) => [...patientKeys.all, 'doctors', filters] as const,
  doctorAvailability: (doctorId: number, date: string) => [...patientKeys.all, 'doctor-availability', doctorId, date] as const,
  dashboardStats: (id: number) => [...patientKeys.all, 'dashboard-stats', id] as const,
};

// Patient Profile Hooks
export const usePatientProfile = (patientId: number) => {
  return useQuery({
    queryKey: patientKeys.profile(patientId),
    queryFn: () => patientService.getProfile(patientId),
    enabled: !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdatePatientProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ patientId, profileData }: { patientId: number; profileData: PatientUpdateRequest }) =>
      patientService.updateProfile(patientId, profileData),
    onSuccess: (data, variables) => {
      queryClient.setQueryData(patientKeys.profile(variables.patientId), data);
      queryClient.invalidateQueries({ queryKey: patientKeys.profile(variables.patientId) });
    },
  });
};

// Appointment Hooks
export const usePatientAppointments = (patientId: number, status?: string) => {
  return useQuery({
    queryKey: patientKeys.appointments(patientId),
    queryFn: () => patientService.getAppointments(patientId, status),
    enabled: !!patientId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCreateAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (appointmentData: AppointmentRequest) =>
      patientService.createAppointment(appointmentData),
    onSuccess: (data, variables) => {
      console.log('✅ Appointment created successfully, invalidating all related caches...');

      // Invalidate ALL appointment-related queries for immediate dashboard update
      queryClient.invalidateQueries({
        queryKey: patientKeys.appointments(variables.patientId)
      });

      // Invalidate dashboard stats to update upcoming appointments count
      queryClient.invalidateQueries({
        queryKey: patientKeys.dashboardStats(variables.patientId)
      });

      // Invalidate doctor availability for the selected date
      const appointmentDate = new Date(variables.appointmentDate).toISOString().split('T')[0];
      queryClient.invalidateQueries({
        queryKey: patientKeys.doctorAvailability(variables.doctorId, appointmentDate)
      });

      // Force refetch of all patient-related data to ensure dashboard updates
      queryClient.refetchQueries({
        queryKey: ['patient', variables.patientId]
      });

      console.log('✅ All caches invalidated and data refetched for immediate dashboard update');
    },
    onError: (error, variables) => {
      console.error('❌ Failed to create appointment:', error);
    }
  });
};

export const useCancelAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ appointmentId, reason }: { appointmentId: number; reason?: string }) =>
      patientService.cancelAppointment(appointmentId, reason),
    onSuccess: (data, variables) => {
      console.log('✅ Appointment cancelled, invalidating caches...');

      // Invalidate all appointment-related queries
      queryClient.invalidateQueries({ queryKey: patientKeys.all });

      console.log('✅ Caches invalidated after appointment cancellation');
    },
    onError: (error) => {
      console.error('❌ Failed to cancel appointment:', error);
    }
  });
};

// Doctor Search Hooks
export const useSearchDoctors = (specialty?: string, location?: string, name?: string) => {
  return useQuery({
    queryKey: patientKeys.doctors({ specialty, location, name }),
    queryFn: () => patientService.searchDoctors(specialty, location, name),
    enabled: !!specialty, // Only enabled when specialty is selected
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false, // Don't retry on failure
    onError: (error) => {
      console.warn('Doctor search failed:', error);
    }
  });
};

export const useDoctorAvailability = (doctorId: number, date: string) => {
  return useQuery({
    queryKey: patientKeys.doctorAvailability(doctorId, date),
    queryFn: () => patientService.getDoctorAvailability(doctorId, date),
    enabled: !!(doctorId && date),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Medical History Hooks
export const usePatientMedicalHistory = (patientId: number) => {
  return useQuery({
    queryKey: patientKeys.medicalHistory(patientId),
    queryFn: () => patientService.getMedicalHistory(patientId),
    enabled: !!patientId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const usePatientDiagnoses = (patientId: number) => {
  return useQuery({
    queryKey: patientKeys.diagnoses(patientId),
    queryFn: () => patientService.getDiagnoses(patientId),
    enabled: !!patientId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const usePatientPrescriptions = (patientId: number) => {
  return useQuery({
    queryKey: patientKeys.prescriptions(patientId),
    queryFn: () => patientService.getPrescriptions(patientId),
    enabled: !!patientId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Dashboard Stats Hook
export const usePatientDashboardStats = (patientId: number) => {
  return useQuery({
    queryKey: patientKeys.dashboardStats(patientId),
    queryFn: () => patientService.getDashboardStats(patientId),
    enabled: !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false, // Don't retry on failure
    onError: (error) => {
      console.warn('Dashboard stats not available:', error);
    }
  });
};

// Appointment History Hook
export const useAppointmentHistory = (patientId: number) => {
  return useQuery({
    queryKey: patientKeys.appointmentHistory(patientId),
    queryFn: () => patientService.getAppointmentHistory(patientId),
    enabled: !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
